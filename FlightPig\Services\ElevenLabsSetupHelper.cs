using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Windows.Forms;
using FlightPig.Models;

namespace FlightPig.Services
{
    /// <summary>
    /// Helper service for ElevenLabs API key setup and configuration
    /// </summary>
    public class ElevenLabsSetupHelper
    {
        private readonly SettingsManager _settingsManager;
        private const string ELEVENLABS_SIGNUP_URL = "https://elevenlabs.io/sign-up";
        private const string ELEVENLABS_API_KEYS_URL = "https://elevenlabs.io/app/speech-synthesis/api-keys";

        public ElevenLabsSetupHelper(SettingsManager settingsManager)
        {
            _settingsManager = settingsManager;
        }

        /// <summary>
        /// Check if ElevenL<PERSON>s is properly configured and guide user if not
        /// </summary>
        public async Task<bool> EnsureElevenLabsConfiguredAsync()
        {
            if (_settingsManager.IsElevenLabsConfigured())
            {
                return true; // Already configured
            }

            // Check if user has explicitly disabled ElevenLabs
            var currentKey = _settingsManager.Settings.ElevenLabs.ApiKey;
            if (currentKey == "DISABLED_BY_USER")
            {
                return false; // User chose to disable, don't ask again
            }

            // Check if user has a placeholder key
            if (string.IsNullOrEmpty(currentKey) || currentKey == "YOUR_ELEVENLABS_API_KEY_HERE")
            {
                return await GuideUserThroughElevenLabsSetupAsync();
            }

            // Key exists but ElevenLabs is disabled - ask user if they want to enable it
            if (!_settingsManager.Settings.ElevenLabs.Enabled)
            {
                Console.WriteLine();
                Console.WriteLine("ElevenLabs API key is configured but text-to-speech is disabled.");
                Console.WriteLine("Would you like to enable text-to-speech? (y/n)");

                var response = Console.ReadKey();
                Console.WriteLine();

                if (response.KeyChar == 'y' || response.KeyChar == 'Y')
                {
                    var settings = _settingsManager.Settings;
                    settings.ElevenLabs.Enabled = true;
                    _settingsManager.UpdateSettings(settings);
                    Console.WriteLine("✓ Text-to-speech enabled!");
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Guide user through ElevenLabs setup process
        /// </summary>
        private async Task<bool> GuideUserThroughElevenLabsSetupAsync()
        {
            Console.WriteLine();
            Console.WriteLine("=== ELEVENLABS TEXT-TO-SPEECH SETUP ===");
            Console.WriteLine();
            Console.WriteLine("FlightPig can use ElevenLabs for high-quality text-to-speech voices.");
            Console.WriteLine("This enhances the experience with realistic pilot and tour guide voices.");
            Console.WriteLine();
            Console.WriteLine("ElevenLabs offers:");
            Console.WriteLine("• Free tier with 10,000 characters per month");
            Console.WriteLine("• High-quality, natural-sounding voices");
            Console.WriteLine("• Multiple voice options for different scenarios");
            Console.WriteLine();
            Console.WriteLine("Would you like to set up ElevenLabs text-to-speech? (y/n/s)");
            Console.WriteLine("  y = Yes, set up now");
            Console.WriteLine("  n = No, use console-only mode");
            Console.WriteLine("  s = Skip for now, ask again next time");

            var response = Console.ReadKey();
            Console.WriteLine();

            if (response.KeyChar == 'y' || response.KeyChar == 'Y')
            {
                return await SetupElevenLabsApiKeyAsync();
            }
            else if (response.KeyChar == 'n' || response.KeyChar == 'N')
            {
                Console.WriteLine("Setting ElevenLabs to disabled. FlightPig will use console-only mode.");
                Console.WriteLine("You can enable it later by editing settings.json");

                // Set a flag to remember user's choice
                var settings = _settingsManager.Settings;
                settings.ElevenLabs.ApiKey = "DISABLED_BY_USER";
                settings.ElevenLabs.Enabled = false;
                _settingsManager.UpdateSettings(settings);
                return false;
            }
            else
            {
                Console.WriteLine("Skipping ElevenLabs setup for now. You'll be asked again next time.");
                return false;
            }
        }

        /// <summary>
        /// Walk user through getting and setting up ElevenLabs API key
        /// </summary>
        private async Task<bool> SetupElevenLabsApiKeyAsync()
        {
            try
            {
                // Show message box with instructions
                var result = MessageBox.Show(
                    "FlightPig will now open the ElevenLabs website where you can:\n\n" +
                    "1. Sign up for a free account (if you don't have one)\n" +
                    "2. Get your API key from the dashboard\n\n" +
                    "After getting your API key, you'll enter it in the next step.\n\n" +
                    "Click OK to open ElevenLabs website, or Cancel to skip setup.",
                    "ElevenLabs Setup - FlightPig",
                    MessageBoxButtons.OKCancel,
                    MessageBoxIcon.Information);

                if (result != DialogResult.OK)
                {
                    Console.WriteLine("ElevenLabs setup cancelled.");
                    return false;
                }

                // Open ElevenLabs signup/API keys page
                await OpenElevenLabsWebsiteAsync();

                // Wait a moment for user to get their key
                Console.WriteLine();
                Console.WriteLine("After you've signed up and obtained your API key:");
                Console.WriteLine("1. Copy your API key from the ElevenLabs dashboard");
                Console.WriteLine("2. Return to this window");
                Console.WriteLine();
                Console.WriteLine("Press any key when you have your API key ready...");
                Console.ReadKey();

                // Prompt for API key
                return await PromptForApiKeyAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during ElevenLabs setup: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Open ElevenLabs website in default browser
        /// </summary>
        private async Task OpenElevenLabsWebsiteAsync()
        {
            try
            {
                string url = ELEVENLABS_API_KEYS_URL; // Go directly to API keys page

                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    Process.Start(new ProcessStartInfo(url) { UseShellExecute = true });
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
                {
                    Process.Start("open", url);
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    Process.Start("xdg-open", url);
                }

                Console.WriteLine($"Opening ElevenLabs website: {url}");
                await Task.Delay(1000); // Give browser time to open
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Could not open browser automatically: {ex.Message}");
                Console.WriteLine($"Please manually visit: {ELEVENLABS_API_KEYS_URL}");
            }
        }

        /// <summary>
        /// Prompt user to enter their API key
        /// </summary>
        private async Task<bool> PromptForApiKeyAsync()
        {
            Console.WriteLine();
            Console.WriteLine("Please enter your ElevenLabs API key:");
            Console.WriteLine("(It should look like: sk-1234567890abcdef...)");
            Console.Write("API Key: ");

            var apiKey = Console.ReadLine()?.Trim();

            if (string.IsNullOrEmpty(apiKey))
            {
                Console.WriteLine("No API key entered. Skipping ElevenLabs setup.");
                return false;
            }

            if (apiKey.Length < 20 || !apiKey.StartsWith("sk-"))
            {
                Console.WriteLine("The API key doesn't look correct. ElevenLabs API keys start with 'sk-' and are longer.");
                Console.WriteLine("Would you like to try again? (y/n)");
                
                var retry = Console.ReadKey();
                Console.WriteLine();

                if (retry.KeyChar == 'y' || retry.KeyChar == 'Y')
                {
                    return await PromptForApiKeyAsync();
                }
                else
                {
                    Console.WriteLine("Skipping ElevenLabs setup.");
                    return false;
                }
            }

            // Save the API key to settings
            var settings = _settingsManager.Settings;
            settings.ElevenLabs.ApiKey = apiKey;
            settings.ElevenLabs.Enabled = true;
            _settingsManager.UpdateSettings(settings);

            Console.WriteLine("✓ ElevenLabs API key saved successfully!");
            Console.WriteLine("✓ Text-to-speech enabled!");
            Console.WriteLine();

            // Test the API key
            Console.WriteLine("Testing ElevenLabs connection...");
            
            try
            {
                // Create a temporary TTS service to test
                var httpClient = new System.Net.Http.HttpClient();
                var ttsService = new TextToSpeechService(httpClient, settings);
                
                await ttsService.SpeakAsync("ElevenLabs text-to-speech is now configured and working!");
                Console.WriteLine("✓ ElevenLabs test successful!");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠ ElevenLabs test failed: {ex.Message}");
                Console.WriteLine("The API key was saved, but there might be an issue with the connection.");
                Console.WriteLine("You can test it later or check your API key in settings.json");
                return true; // Still return true since we saved the key
            }
        }

        /// <summary>
        /// Show information about ElevenLabs configuration
        /// </summary>
        public void ShowElevenLabsInfo()
        {
            Console.WriteLine();
            Console.WriteLine("=== ELEVENLABS INFORMATION ===");
            
            if (_settingsManager.IsElevenLabsConfigured())
            {
                Console.WriteLine("✓ ElevenLabs is configured and enabled");
                Console.WriteLine($"  API Key: {_settingsManager.Settings.ElevenLabs.ApiKey.Substring(0, 8)}...");
                Console.WriteLine($"  Default Voice: {_settingsManager.Settings.ElevenLabs.DefaultVoiceId}");
                Console.WriteLine($"  Pilot Voice: {_settingsManager.Settings.ElevenLabs.PilotVoiceId}");
            }
            else
            {
                Console.WriteLine("✗ ElevenLabs is not configured");
                Console.WriteLine("  Text-to-speech will use console output only");
                Console.WriteLine("  Run setup again to configure ElevenLabs");
            }
            
            Console.WriteLine();
        }

        /// <summary>
        /// Reset ElevenLabs configuration
        /// </summary>
        public void ResetElevenLabsConfiguration()
        {
            var settings = _settingsManager.Settings;
            settings.ElevenLabs.ApiKey = "YOUR_ELEVENLABS_API_KEY_HERE";
            settings.ElevenLabs.Enabled = false;
            _settingsManager.UpdateSettings(settings);
            
            Console.WriteLine("ElevenLabs configuration reset. Run setup again to reconfigure.");
        }
    }
}
