using System;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using CTrue.FsConnect;
using FlightPig.Models;
using FlightPig.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Http;

namespace FlightPig
{
    class Program
    {
        private static FlightSimService _flightSimService;
        private static OllamaService _ollamaService;
        private static MissionProgressMonitor _missionMonitor;
        private static TourManager _tourManager;
        private static AirportService _airportService;
        private static LandingEvaluator _landingEvaluator;
        private static SettingsManager _settingsManager;
        private static SpeechRecognitionService _speechService;
        private static TextToSpeechService _ttsService;
        private static VoiceCommandProcessor _voiceProcessor;
        private static bool _voiceMode = false;
        private static LandingChallenge _currentLandingChallenge;
        private static BushFlyingRouteGenerator _bushFlyingGenerator;
        private static BushFlyingEvaluator _bushFlyingEvaluator;
        private static System.Threading.Timer _connectionRetryTimer;
        private static WeatherService _weatherService;
        private static BushFlyingChallenge _currentBushFlyingChallenge;

        static async Task Main(string[] args)
        {
            Console.WriteLine("=== FLIGHT PIG ===");
            Console.WriteLine("AI-Powered Flight Mission Generator for MSFS");
            Console.WriteLine("With Voice Recognition and Text-to-Speech");
            Console.WriteLine();

            // Initialize settings
            _settingsManager = new SettingsManager();
            _settingsManager.DisplaySettings();

            // Setup services
            var services = new ServiceCollection();
            services.AddHttpClient();
            var serviceProvider = services.BuildServiceProvider();
            var httpClientFactory = serviceProvider.GetRequiredService<IHttpClientFactory>();
            var httpClient = httpClientFactory.CreateClient();

            _ollamaService = new OllamaService(httpClient, _settingsManager.Settings.Ollama.BaseUrl, _settingsManager.Settings.Ollama.ModelName);
            _missionMonitor = new MissionProgressMonitor();
            _tourManager = new TourManager();
            _airportService = new AirportService();
            _landingEvaluator = new LandingEvaluator();
            _flightSimService = new FlightSimService();

            // Initialize voice services
            _ttsService = new TextToSpeechService(httpClientFactory.CreateClient(), _settingsManager.Settings);
            _speechService = new SpeechRecognitionService(_settingsManager.Settings);
            _voiceProcessor = new VoiceCommandProcessor(_ttsService);

            // Setup event handlers
            _voiceProcessor.CommandRecognized += OnVoiceCommandRecognized;
            _missionMonitor.TourPoiReached += OnTourPoiReached;
            _missionMonitor.LandingChallengeCompleted += OnLandingChallengeCompleted;
            _tourManager.PoiReached += OnTourManagerPoiReached;
            _tourManager.TourCompleted += OnTourCompleted;
            _landingEvaluator.LandingCompleted += OnLandingEvaluationCompleted;
            _flightSimService.AircraftDataReceived += OnAircraftDataReceived;

            // Setup ElevenLabs if needed
            var elevenLabsHelper = new ElevenLabsSetupHelper(_settingsManager);
            await elevenLabsHelper.EnsureElevenLabsConfiguredAsync();

            // Recreate TTS service after ElevenLabs setup to ensure API key is properly configured
            _ttsService?.Dispose();
            _ttsService = new TextToSpeechService(httpClientFactory.CreateClient(), _settingsManager.Settings);
            _voiceProcessor = new VoiceCommandProcessor(_ttsService);

            // Test voice services
            if (_settingsManager.IsVoiceEnabled())
            {
                Console.WriteLine("Voice recognition is enabled.");
                if (_settingsManager.IsElevenLabsConfigured())
                {
                    Console.WriteLine("Text-to-speech is configured.");
                    await _ttsService.SpeakAsync("FlightPig voice services initialized.");
                }
                else
                {
                    Console.WriteLine("Text-to-speech is not configured - using console output only.");
                }
            }

            // Initialize Ollama with automatic installation
            Console.WriteLine("Initializing Ollama...");
            var ollamaInstaller = new OllamaInstaller(httpClient);

            if (!await ollamaInstaller.IsOllamaAvailableAsync())
            {
                Console.WriteLine("Ollama is not available. Checking installation...");

                if (!await ollamaInstaller.GuideUserThroughInstallationAsync())
                {
                    Console.WriteLine("Ollama setup was not completed. FlightPig requires Ollama to function.");
                    if (_ttsService != null)
                        await _ttsService.SpeakErrorAsync("Ollama setup required");
                    Console.WriteLine("Press any key to exit...");
                    Console.ReadKey();
                    return;
                }
            }

            var modelReady = await _ollamaService.PullModelAsync();
            if (!modelReady)
            {
                Console.WriteLine("Failed to initialize Ollama model. Please ensure Ollama is running and try again.");
                if (_ttsService != null)
                    await _ttsService.SpeakErrorAsync("Failed to initialize Ollama model");
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
                return;
            }

            // Load airports data
            Console.WriteLine("Loading airports database...");
            await _airportService.LoadAirportsAsync();

            // Try to connect to MSFS, but don't exit if it fails
            Console.WriteLine("Connecting to Microsoft Flight Simulator...");
            if (_flightSimService.TryConnect())
            {
                Console.WriteLine("✓ Connected to MSFS successfully!");
                if (_ttsService != null)
                    await _ttsService.SpeakAsync("Connected to Microsoft Flight Simulator successfully.");
            }
            else
            {
                Console.WriteLine("⚠ MSFS not detected - FlightPig will continue without real-time flight data");
                Console.WriteLine("  Start MSFS anytime and FlightPig will automatically connect");
                if (_ttsService != null)
                    await _ttsService.SpeakAsync("Microsoft Flight Simulator not detected. FlightPig will continue in offline mode.");

                // Start background connection retry
                StartConnectionRetryTimer();
            }

            // Initialize services that depend on FlightSimService
            _weatherService = new WeatherService(_flightSimService);
            _bushFlyingGenerator = new BushFlyingRouteGenerator(_airportService, _ttsService, _weatherService);
            _bushFlyingEvaluator = new BushFlyingEvaluator(_missionMonitor);

            Console.WriteLine();

            // Main menu loop
            await RunMainMenuAsync();

            // Cleanup
            _connectionRetryTimer?.Dispose();
            _flightSimService?.Disconnect();

            _speechService?.Dispose();
            _ttsService?.Dispose();

            Console.WriteLine("Goodbye!");
            if (_ttsService != null)
                await _ttsService.SpeakAsync("Goodbye!");
        }



        private static void StartConnectionRetryTimer()
        {
            // Try to reconnect every 10 seconds
            _connectionRetryTimer = new System.Threading.Timer(TryReconnectToMSFS, null,
                TimeSpan.FromSeconds(10), TimeSpan.FromSeconds(10));
        }

        private static async void TryReconnectToMSFS(object state)
        {
            if (_flightSimService.IsConnected) return; // Already connected

            try
            {
                if (_flightSimService.TryConnect())
                {
                    _connectionRetryTimer?.Dispose();
                    _connectionRetryTimer = null;

                    Console.WriteLine();
                    Console.WriteLine("✓ Connected to MSFS successfully!");
                    if (_ttsService != null)
                        await _ttsService.SpeakAsync("Connected to Microsoft Flight Simulator successfully.");
                }
            }
            catch
            {
                // Silently continue trying
            }
        }

        private static bool IsMSFSConnectedWithAircraft()
        {
            return _flightSimService.IsConnectedWithAircraft;
        }

        private static void ShowMSFSNotConnectedMessage()
        {
            Console.WriteLine("❌ Microsoft Flight Simulator is not connected or no aircraft is loaded.");
            Console.WriteLine("   Please start MSFS and load an aircraft, then try again.");
            Console.WriteLine("   FlightPig will automatically connect when MSFS is available.");
            Console.WriteLine();
            Console.WriteLine("Press any key to return to menu...");
        }



        private static void OnAircraftDataReceived(object sender, AircraftInfo aircraftInfo)
        {
            if (_missionMonitor != null)
                _missionMonitor.UpdateProgress(aircraftInfo);

            if (_tourManager != null && _tourManager.CurrentTour != null)
                _tourManager.CheckPoiReached(aircraftInfo);

            if (_landingEvaluator != null)
                _landingEvaluator.UpdateFlightData(aircraftInfo);
        }

        private static async Task RunMainMenuAsync()
        {
            while (true)
            {
                Console.Clear();
                Console.WriteLine("=== FLIGHT PIG - MAIN MENU ===");
                Console.WriteLine();
                Console.WriteLine("1. Start Story Mission");
                Console.WriteLine("2. Start Guided Tour");
                Console.WriteLine("3. Start Landing Challenge");
                Console.WriteLine("4. Start Bush Flying Challenge");
                Console.WriteLine("5. Show Current Aircraft Info");
                Console.WriteLine("6. Monitor Mission Progress");
                Console.WriteLine("7. Toggle Voice Mode");
                Console.WriteLine("8. Test Voice");
                Console.WriteLine("9. Settings");
                Console.WriteLine("0. Exit");
                Console.WriteLine();

                if (_voiceMode)
                {
                    Console.WriteLine("🎤 VOICE MODE ACTIVE - Say a command or press any key for keyboard input");
                    Console.WriteLine("Voice commands: 'start mission', 'start tour', 'landing challenge', 'show status', 'monitor progress', 'exit'");
                }
                else
                {
                    Console.Write("Select an option (0-9) or press 'V' for voice mode: ");
                }

                if (_voiceMode && _settingsManager.IsVoiceEnabled())
                {
                    // Voice input mode
                    var speechText = await _speechService.RecordSingleCommandAsync();
                    if (!string.IsNullOrEmpty(speechText))
                    {
                        var command = await _voiceProcessor.ProcessSpeechAsync(speechText);
                        if (command != null)
                        {
                            await HandleVoiceCommand(command.Action);
                            continue;
                        }
                    }

                    // Fall back to keyboard if no voice command recognized
                    _voiceMode = false;
                    continue;
                }

                var key = Console.ReadKey();
                Console.WriteLine();

                switch (key.KeyChar)
                {
                    case '1':
                        await StartStoryMissionAsync();
                        break;
                    case '2':
                        await StartGuidedTourAsync();
                        break;
                    case '3':
                        await StartLandingChallengeAsync();
                        break;
                    case '4':
                        await StartBushFlyingChallengeAsync();
                        break;
                    case '5':
                        await ShowCurrentAircraftInfoAsync();
                        break;
                    case '6':
                        await MonitorMissionProgressAsync();
                        break;
                    case '7':
                        ToggleVoiceMode();
                        break;
                    case '8':
                        await TestVoiceAsync();
                        break;
                    case '9':
                        ShowSettings();
                        break;
                    case '0':
                        return;
                    case 'v':
                    case 'V':
                        ToggleVoiceMode();
                        break;
                    default:
                        Console.WriteLine("Invalid option. Press any key to continue...");
                        Console.ReadKey();
                        break;
                }
            }
        }

        private static async Task StartStoryMissionAsync()
        {
            Console.Clear();
            Console.WriteLine("=== STARTING STORY MISSION ===");
            Console.WriteLine();

            // Get current aircraft info
            Console.WriteLine("Getting current aircraft information...");
            RequestCurrentAircraftInfo();

            // Wait a moment for data to arrive
            await Task.Delay(1000);

            if (!IsMSFSConnectedWithAircraft())
            {
                ShowMSFSNotConnectedMessage();
                Console.ReadKey();
                return;
            }

            var aircraftInfo = _flightSimService.CurrentAircraftInfo;
            Console.WriteLine($"Aircraft: {aircraftInfo.Title}");
            Console.WriteLine($"Location: {aircraftInfo.Latitude:F4}, {aircraftInfo.Longitude:F4}");
            Console.WriteLine($"Altitude: {aircraftInfo.Altitude:F0} ft");
            Console.WriteLine();

            Console.WriteLine("Generating mission with AI...");
            if (_ttsService != null)
                await _ttsService.SpeakAsync("Generating mission with AI...");

            var mission = await _ollamaService.GenerateMissionAsync(aircraftInfo);

            if (mission == null)
            {
                Console.WriteLine("Failed to generate mission. Please try again.");
                if (_ttsService != null)
                    await _ttsService.SpeakErrorAsync("Failed to generate mission");
                Console.WriteLine("Press any key to return to menu...");
                Console.ReadKey();
                return;
            }

            // Display mission
            Console.Clear();
            Console.WriteLine("=== MISSION GENERATED ===");
            Console.WriteLine();
            Console.WriteLine($"Title: {mission.Title}");
            Console.WriteLine($"Description: {mission.Description}");
            Console.WriteLine();
            Console.WriteLine("Objectives:");

            for (int i = 0; i < mission.Objectives.Count; i++)
            {
                var obj = mission.Objectives[i];
                Console.WriteLine($"{i + 1}. {obj.Title}");
                Console.WriteLine($"   {obj.Description}");
                Console.WriteLine($"   Type: {obj.Type}");
                if (obj.Latitude.HasValue && obj.Longitude.HasValue)
                {
                    Console.WriteLine($"   Location: {obj.Latitude:F4}, {obj.Longitude:F4}");
                }
                if (obj.Altitude.HasValue)
                {
                    Console.WriteLine($"   Altitude: {obj.Altitude:F0} ft");
                }
                Console.WriteLine();
            }

            // Speak the mission details
            if (_ttsService != null)
            {
                await _ttsService.SpeakMissionAsync(mission);
            }

            Console.WriteLine("Press 'S' to start this mission, or any other key to return to menu...");
            var response = Console.ReadKey();

            if (response.KeyChar == 's' || response.KeyChar == 'S')
            {
                _missionMonitor.CurrentMission = mission;
                Console.WriteLine();
                Console.WriteLine("Mission started! Monitoring progress...");
                if (_ttsService != null)
                    await _ttsService.SpeakAsync("Mission started! Monitoring progress...");
                await Task.Delay(2000);
            }
        }

        private static async Task ShowCurrentAircraftInfoAsync()
        {
            Console.Clear();
            Console.WriteLine("=== CURRENT AIRCRAFT INFORMATION ===");
            Console.WriteLine();

            RequestCurrentAircraftInfo();

            // Wait for data
            Thread.Sleep(500);

            if (!IsMSFSConnectedWithAircraft())
            {
                ShowMSFSNotConnectedMessage();
                if (_ttsService != null)
                    await _ttsService.SpeakErrorAsync("Microsoft Flight Simulator not connected");
            }
            else
            {
                var aircraftInfo = _flightSimService.CurrentAircraftInfo;
                Console.WriteLine($"Aircraft: {aircraftInfo.Title}");
                Console.WriteLine($"Position: {aircraftInfo.Latitude:F6}, {aircraftInfo.Longitude:F6}");
                Console.WriteLine($"Altitude: {aircraftInfo.Altitude:F0} ft");
                Console.WriteLine($"Heading: {aircraftInfo.Heading:F0}°");
                Console.WriteLine($"Airspeed: {aircraftInfo.AirspeedKnots:F0} knots");
                Console.WriteLine($"Ground Speed: {aircraftInfo.GroundSpeedKnots:F0} knots");
                Console.WriteLine($"Vertical Speed: {aircraftInfo.VerticalSpeedFpm:F0} fpm");
                Console.WriteLine($"On Ground: {(aircraftInfo.OnGround ? "Yes" : "No")}");

                if (_ttsService != null)
                    await _ttsService.SpeakAircraftStatusAsync(aircraftInfo);
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to return to menu...");
            Console.ReadKey();
        }

        private static async Task MonitorMissionProgressAsync()
        {
            if (_missionMonitor == null || _missionMonitor.CurrentMission == null)
            {
                Console.WriteLine("No active mission. Please start a mission first.");
                Console.WriteLine("Press any key to return to menu...");
                Console.ReadKey();
                return;
            }

            Console.WriteLine("Starting mission monitoring...");
            Console.WriteLine("Press ESC to return to menu.");
            await Task.Delay(1000);

            while (true)
            {
                RequestCurrentAircraftInfo();
                await Task.Delay(1000);

                var key = Console.ReadKey(true);
                if (key.Key == ConsoleKey.Escape)
                {
                    break;
                }
            }
        }

        private static void RequestCurrentAircraftInfo()
        {
            _flightSimService.RequestAircraftData();
        }

        private static async void OnVoiceCommandRecognized(object sender, VoiceCommandEventArgs e)
        {
            await HandleVoiceCommand(e.Command.Action);
        }

        private static async Task HandleVoiceCommand(VoiceCommandAction action)
        {
            switch (action)
            {
                case VoiceCommandAction.StartMission:
                    await StartStoryMissionAsync();
                    break;
                case VoiceCommandAction.StartTour:
                    await StartGuidedTourAsync();
                    break;
                case VoiceCommandAction.StartLandingChallenge:
                    await StartLandingChallengeAsync();
                    break;
                case VoiceCommandAction.ShowStatus:
                    await ShowCurrentAircraftInfoAsync();
                    break;
                case VoiceCommandAction.MonitorProgress:
                    await MonitorMissionProgressAsync();
                    break;
                case VoiceCommandAction.Exit:
                    Environment.Exit(0);
                    break;
                case VoiceCommandAction.Help:
                    await ShowVoiceHelpAsync();
                    break;
                case VoiceCommandAction.Settings:
                    ShowSettings();
                    break;
                case VoiceCommandAction.TestVoice:
                    await TestVoiceAsync();
                    break;
                case VoiceCommandAction.AcceptTour:
                    // This would be handled in tour selection context
                    if (_ttsService != null)
                        await _ttsService.SpeakAsync("Tour accepted");
                    break;
                case VoiceCommandAction.DeclineTour:
                    // This would be handled in tour selection context
                    if (_ttsService != null)
                        await _ttsService.SpeakAsync("Tour declined");
                    break;
                case VoiceCommandAction.RepeatInformation:
                    // This would repeat the last tour information
                    if (_ttsService != null)
                        await _ttsService.SpeakAsync("Repeating last information");
                    break;
                case VoiceCommandAction.NextPoi:
                    // This would skip to next POI in active tour
                    if (_ttsService != null)
                        await _ttsService.SpeakAsync("Moving to next point of interest");
                    break;
                case VoiceCommandAction.AcceptLandingChallenge:
                    // This would be handled in landing challenge selection context
                    if (_ttsService != null)
                        await _ttsService.SpeakAsync("Landing challenge accepted");
                    break;
                case VoiceCommandAction.DeclineLandingChallenge:
                    // This would be handled in landing challenge selection context
                    if (_ttsService != null)
                        await _ttsService.SpeakAsync("Landing challenge declined");
                    break;
                case VoiceCommandAction.MainMenu:
                    // Return to main menu (do nothing, let loop continue)
                    break;
            }
        }

        private static void ToggleVoiceMode()
        {
            _voiceMode = !_voiceMode;
            var status = _voiceMode ? "enabled" : "disabled";
            Console.WriteLine($"Voice mode {status}.");

            if (_ttsService != null)
                _ = _ttsService.SpeakAsync($"Voice mode {status}");
        }

        private static async Task TestVoiceAsync()
        {
            Console.WriteLine("Testing voice services...");

            if (_ttsService != null)
            {
                await _ttsService.TestSpeechAsync();
            }
            else
            {
                Console.WriteLine("Text-to-speech service not available.");
            }

            Console.WriteLine("Press any key to continue...");
            Console.ReadKey();
        }

        private static async Task ShowVoiceHelpAsync()
        {
            Console.WriteLine("=== VOICE COMMANDS ===");
            var commands = _voiceProcessor.GetAllCommands();

            foreach (var command in commands)
            {
                Console.WriteLine($"• {command.Patterns[0]} - {command.Description}");
            }

            if (_ttsService != null)
            {
                await _voiceProcessor.SpeakHelpAsync();
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to continue...");
            Console.ReadKey();
        }

        private static async Task StartGuidedTourAsync()
        {
            Console.Clear();
            Console.WriteLine("=== STARTING GUIDED TOUR ===");
            Console.WriteLine();

            // Get current aircraft info
            Console.WriteLine("Getting current aircraft information...");
            RequestCurrentAircraftInfo();

            // Wait a moment for data to arrive
            await Task.Delay(1000);

            if (!IsMSFSConnectedWithAircraft())
            {
                ShowMSFSNotConnectedMessage();
                Console.ReadKey();
                return;
            }

            var aircraftInfo = _flightSimService.CurrentAircraftInfo;
            Console.WriteLine($"Aircraft: {aircraftInfo.Title}");
            Console.WriteLine($"Location: {aircraftInfo.Latitude:F4}, {aircraftInfo.Longitude:F4}");
            Console.WriteLine($"Altitude: {aircraftInfo.Altitude:F0} ft");
            Console.WriteLine();

            Console.WriteLine("Generating guided tour with AI...");
            if (_ttsService != null)
                await _ttsService.SpeakAsync("Generating guided tour with AI...");

            var tour = await _ollamaService.GenerateTourAsync(aircraftInfo);

            if (tour == null)
            {
                Console.WriteLine("Failed to generate tour. Please try again.");
                if (_ttsService != null)
                    await _ttsService.SpeakErrorAsync("Failed to generate tour");
                Console.WriteLine("Press any key to return to menu...");
                Console.ReadKey();
                return;
            }

            // Display tour information
            Console.Clear();
            Console.WriteLine("=== GENERATED GUIDED TOUR ===");
            Console.WriteLine($"Title: {tour.Title}");
            Console.WriteLine($"Description: {tour.Description}");
            Console.WriteLine($"Region: {tour.Region}");
            Console.WriteLine($"Aircraft Type: {tour.RecommendedAircraft.FirstOrDefault() ?? "General Aviation"}");
            Console.WriteLine($"Estimated Duration: {tour.EstimatedDurationMinutes} minutes");
            Console.WriteLine($"Altitude Range: {tour.MinimumAltitude}-{tour.MaximumAltitude} feet");
            Console.WriteLine($"Points of Interest: {tour.PointsOfInterest.Count}");
            Console.WriteLine();

            // List POIs
            Console.WriteLine("Tour Route:");
            for (int i = 0; i < tour.PointsOfInterest.Count; i++)
            {
                var poi = tour.PointsOfInterest[i];
                Console.WriteLine($"{i + 1}. {poi.Name} - {poi.Description}");
            }
            Console.WriteLine();

            // Speak the tour introduction with aircraft context
            if (_ttsService != null)
            {
                await _ttsService.SpeakTourIntroductionAsync(tour, aircraftInfo.Title);
            }

            Console.WriteLine("Press 'S' to start this tour, or any other key to return to menu...");
            var response = Console.ReadKey();

            if (response.KeyChar == 's' || response.KeyChar == 'S')
            {
                // Convert tour to mission and start monitoring
                var mission = tour.ToMission();
                _missionMonitor.CurrentMission = mission;
                _tourManager.AddGeneratedTour(tour);
                _tourManager.StartTour(tour.Id);

                Console.WriteLine();
                Console.WriteLine("Guided tour started! Follow the instructions and enjoy the journey...");
                if (_ttsService != null)
                    await _ttsService.SpeakAsync("Guided tour started! Follow the instructions and enjoy the journey...");
                await Task.Delay(2000);
            }
        }

        private static async void OnTourPoiReached(object sender, TourPoiReachedEventArgs e)
        {
            if (_ttsService != null)
            {
                await _ttsService.SpeakPoiInformationAsync(e.Objective.Title, e.TourGuideText, e.NextPoiInstructions);
            }
        }

        private static async void OnTourManagerPoiReached(object sender, PoiReachedEventArgs e)
        {
            Console.WriteLine($"🎯 Reached POI: {e.Poi.Name}");

            if (_ttsService != null)
            {
                await _ttsService.SpeakPoiInformationAsync(e.Poi.Name, e.Poi.TourGuideText, e.Poi.NextPoiInstructions);
            }
        }

        private static async void OnTourCompleted(object sender, TourCompletedEventArgs e)
        {
            Console.WriteLine($"🎉 Tour completed: {e.Tour.Title}");

            if (_ttsService != null)
            {
                await _ttsService.SpeakTourConclusionAsync(e.Tour);
            }
        }

        private static async Task StartLandingChallengeAsync()
        {
            Console.Clear();
            Console.WriteLine("=== LANDING CHALLENGE ===");
            Console.WriteLine();

            // Get current aircraft info
            Console.WriteLine("Getting current aircraft information...");
            RequestCurrentAircraftInfo();

            // Wait a moment for data to arrive
            await Task.Delay(1000);

            if (!IsMSFSConnectedWithAircraft())
            {
                ShowMSFSNotConnectedMessage();
                Console.ReadKey();
                return;
            }

            var aircraftInfo = _flightSimService.CurrentAircraftInfo;
            Console.WriteLine($"Aircraft: {aircraftInfo.Title}");
            Console.WriteLine($"Current Location: {aircraftInfo.Latitude:F4}, {aircraftInfo.Longitude:F4}");
            Console.WriteLine($"Altitude: {aircraftInfo.Altitude:F0} ft");
            Console.WriteLine();

            Console.WriteLine("Finding nearest suitable airport...");
            if (_ttsService != null)
                await _ttsService.SpeakAsync("Finding nearest suitable airport for landing challenge...");

            var challenge = _airportService.CreateLandingChallenge(aircraftInfo);

            if (challenge == null)
            {
                Console.WriteLine("No suitable airports found within range for a landing challenge.");
                if (_ttsService != null)
                    await _ttsService.SpeakErrorAsync("No suitable airports found for landing challenge");
                Console.WriteLine("Press any key to return to menu...");
                Console.ReadKey();
                return;
            }

            // Display challenge information
            Console.Clear();
            Console.WriteLine("=== LANDING CHALLENGE GENERATED ===");
            Console.WriteLine($"Airport: {challenge.Airport.Name} ({challenge.Airport.IcaoCode})");
            Console.WriteLine($"Runway: {challenge.TargetRunway.Designation}");
            Console.WriteLine($"Length: {challenge.TargetRunway.LengthFeet:F0} feet");
            Console.WriteLine($"Surface: {challenge.TargetRunway.Surface}");
            Console.WriteLine($"Difficulty: {challenge.Difficulty}");
            Console.WriteLine($"Distance: {challenge.Description.Split("Distance: ")[1]}");
            Console.WriteLine($"Approach Type: {challenge.ApproachType}");
            Console.WriteLine();
            Console.WriteLine("Challenge: Execute a precision landing with proper approach, touchdown, and rollout.");
            Console.WriteLine("You will be scored on approach stability, touchdown precision, and overall technique.");
            Console.WriteLine();

            // Speak the challenge details
            if (_ttsService != null)
            {
                var challengeText = $"Landing challenge at {challenge.Airport.Name}. " +
                                  $"Your target is runway {challenge.TargetRunway.Designation}, " +
                                  $"which is {challenge.TargetRunway.LengthFeet:F0} feet long. " +
                                  $"This is a {challenge.Difficulty.ToString().ToLower()} difficulty challenge. " +
                                  $"You will be evaluated on approach stability, touchdown precision, and rollout control.";
                await _ttsService.SpeakAsync(challengeText, isPilotVoice: false);
            }

            Console.WriteLine("Press 'S' to start this landing challenge, or any other key to return to menu...");
            var response = Console.ReadKey();

            if (response.KeyChar == 's' || response.KeyChar == 'S')
            {
                // Start the landing challenge
                _currentLandingChallenge = challenge;
                challenge.IsActive = true;
                challenge.StartTime = DateTime.Now;

                // Convert to mission and start monitoring
                var mission = challenge.ToMission();
                _missionMonitor.CurrentMission = mission;

                // Start landing evaluation
                _landingEvaluator.StartEvaluation(challenge);

                Console.WriteLine();
                Console.WriteLine("Landing challenge started! Fly to the airport and execute your landing.");
                Console.WriteLine("Your performance is being monitored and will be analyzed upon completion.");

                if (_ttsService != null)
                    await _ttsService.SpeakAsync("Landing challenge started! Good luck with your approach and landing.", isPilotVoice: false);

                await Task.Delay(3000);
            }
        }

        private static async Task StartBushFlyingChallengeAsync()
        {
            Console.Clear();
            Console.WriteLine("=== BUSH FLYING CHALLENGE ===");
            Console.WriteLine();

            if (!IsMSFSConnectedWithAircraft())
            {
                ShowMSFSNotConnectedMessage();
                Console.ReadKey();
                return;
            }

            Console.WriteLine("🌲 Generating bush flying adventure...");
            var aircraftInfo = _flightSimService.CurrentAircraftInfo;
            Console.WriteLine($"Aircraft: {aircraftInfo.Title}");
            Console.WriteLine($"Current Position: {aircraftInfo.Latitude:F4}, {aircraftInfo.Longitude:F4}");
            Console.WriteLine();

            try
            {
                // Determine aircraft type
                var aircraftType = DetermineAircraftType(aircraftInfo.Title);

                // Select difficulty
                Console.WriteLine("Select bush flying difficulty:");
                Console.WriteLine("1. Beginner - Easy terrain, good weather");
                Console.WriteLine("2. Intermediate - Moderate challenges");
                Console.WriteLine("3. Advanced - Difficult terrain and weather");
                Console.WriteLine("4. Expert - Extreme conditions");
                Console.WriteLine("5. Legendary - The ultimate bush flying test");
                Console.Write("Enter difficulty (1-5): ");

                var difficultyChoice = Console.ReadKey().KeyChar;
                Console.WriteLine();

                var difficulty = difficultyChoice switch
                {
                    '1' => BushFlyingDifficulty.Beginner,
                    '2' => BushFlyingDifficulty.Intermediate,
                    '3' => BushFlyingDifficulty.Advanced,
                    '4' => BushFlyingDifficulty.Expert,
                    '5' => BushFlyingDifficulty.Legendary,
                    _ => BushFlyingDifficulty.Intermediate
                };

                // Generate bush flying challenge
                var challenge = await _bushFlyingGenerator.GenerateBushFlyingChallengeAsync(
                    aircraftInfo.Latitude,
                    aircraftInfo.Longitude,
                    aircraftType,
                    difficulty);

                Console.Clear();
                Console.WriteLine("=== BUSH FLYING CHALLENGE GENERATED ===");
                Console.WriteLine();
                Console.WriteLine($"🏔️ {challenge.Title}");
                Console.WriteLine($"📍 Region: {challenge.Region}");
                Console.WriteLine($"🛩️ Aircraft: {aircraftType}");
                Console.WriteLine($"⭐ Difficulty: {challenge.Difficulty}");
                Console.WriteLine($"📏 Total Distance: {challenge.TotalDistanceNM:F0} NM");
                Console.WriteLine($"⏱️ Estimated Duration: {challenge.EstimatedDurationMinutes:F0} minutes");
                Console.WriteLine($"🌤️ Weather: {challenge.WeatherConditions}");
                Console.WriteLine($"🗻 Terrain: {challenge.TerrainType}");
                Console.WriteLine();
                Console.WriteLine($"📖 Scenario: {challenge.Scenario}");
                Console.WriteLine();
                Console.WriteLine($"🎯 Waypoints ({challenge.Waypoints.Count}):");
                for (int i = 0; i < Math.Min(challenge.Waypoints.Count, 5); i++)
                {
                    var wp = challenge.Waypoints[i];
                    Console.WriteLine($"  {i + 1}. {wp.Name} ({wp.Type})");
                }
                if (challenge.Waypoints.Count > 5)
                    Console.WriteLine($"  ... and {challenge.Waypoints.Count - 5} more waypoints");

                Console.WriteLine();
                Console.WriteLine($"🎯 Required Skills: {string.Join(", ", challenge.RequiredSkills)}");
                Console.WriteLine($"⚠️ Challenges: {string.Join(", ", challenge.Challenges)}");
                Console.WriteLine();

                // Provide TTS briefing
                if (_ttsService != null)
                {
                    await _ttsService.ProvideBushFlyingBriefingAsync(challenge);
                }

                Console.WriteLine("Press 'S' to start this bush flying challenge, or any other key to return to menu...");
                var response = Console.ReadKey();

                if (response.KeyChar == 's' || response.KeyChar == 'S')
                {
                    // Start the bush flying challenge
                    _currentBushFlyingChallenge = challenge;
                    _bushFlyingEvaluator.StartChallenge(challenge);

                    Console.WriteLine();
                    Console.WriteLine("🌲 Bush flying challenge started!");
                    Console.WriteLine("Navigate to your first waypoint to begin the adventure.");
                    Console.WriteLine("Your bush flying skills will be continuously evaluated.");

                    if (_ttsService != null)
                    {
                        await _ttsService.SpeakAsync("Bush flying challenge started! Navigate to your first waypoint and demonstrate your bush flying skills. Safe flying!", isPilotVoice: false);
                    }

                    await Task.Delay(3000);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error generating bush flying challenge: {ex.Message}");
                Console.WriteLine("Press any key to return to menu...");
                Console.ReadKey();
            }
        }

        private static async void OnLandingChallengeCompleted(object sender, LandingChallengeCompletedEventArgs e)
        {
            Console.WriteLine($"🛬 Landing challenge objective completed: {e.Objective.Title}");

            if (_ttsService != null)
            {
                await _ttsService.SpeakAsync("Landing detected. Analyzing your performance...", isPilotVoice: false);
            }
        }

        private static async void OnLandingEvaluationCompleted(object sender, LandingCompletedEventArgs e)
        {
            Console.WriteLine($"📊 Landing evaluation completed for {e.Challenge.Airport.Name}");

            if (_ttsService != null)
            {
                await _ttsService.SpeakLandingAnalysisAsync(e.Challenge.Score, e.Challenge.AircraftType);
            }

            // Display detailed score breakdown
            Console.WriteLine();
            Console.WriteLine("=== LANDING PERFORMANCE REPORT ===");
            Console.WriteLine($"Overall Score: {e.Challenge.Score.OverallScore:F1}% (Grade: {e.Challenge.Score.Grade})");
            Console.WriteLine($"Approach: {e.Challenge.Score.ApproachScore:F1}%");
            Console.WriteLine($"Touchdown: {e.Challenge.Score.TouchdownScore:F1}%");
            Console.WriteLine($"Rollout: {e.Challenge.Score.RolloutScore:F1}%");
            Console.WriteLine($"Stability: {e.Challenge.Score.StabilityScore:F1}%");

            if (e.Challenge.Score.Achievements.Count > 0)
            {
                Console.WriteLine();
                Console.WriteLine("🏆 Achievements:");
                foreach (var achievement in e.Challenge.Score.Achievements)
                {
                    Console.WriteLine($"  • {achievement}");
                }
            }

            _currentLandingChallenge = null;
        }

        private static string DetermineAircraftType(string aircraftTitle)
        {
            if (string.IsNullOrEmpty(aircraftTitle))
                return "General Aviation";

            var title = aircraftTitle.ToLower();

            if (title.Contains("helicopter") || title.Contains("heli"))
                return "Helicopter";
            if (title.Contains("jet") || title.Contains("boeing") || title.Contains("airbus"))
                return "Jet";
            if (title.Contains("turboprop") || title.Contains("king air"))
                return "Turboprop";
            if (title.Contains("glider") || title.Contains("sailplane"))
                return "Glider";
            if (title.Contains("seaplane") || title.Contains("floatplane"))
                return "Seaplane";

            return "General Aviation";
        }

        private static async void ShowSettings()
        {
            Console.Clear();
            Console.WriteLine("=== SETTINGS & CONFIGURATION ===");
            Console.WriteLine();

            _settingsManager.DisplaySettings();

            Console.WriteLine("=== SETUP OPTIONS ===");
            Console.WriteLine("1. Reconfigure ElevenLabs (Text-to-Speech)");
            Console.WriteLine("2. Check Ollama Status");
            Console.WriteLine("3. Test Voice Services");
            Console.WriteLine("4. Reset ElevenLabs Configuration");
            Console.WriteLine("5. Edit settings.json manually");
            Console.WriteLine("0. Return to Main Menu");
            Console.WriteLine();
            Console.Write("Select an option (0-5): ");

            var choice = Console.ReadKey();
            Console.WriteLine();
            Console.WriteLine();

            switch (choice.KeyChar)
            {
                case '1':
                    await ReconfigureElevenLabsAsync();
                    break;
                case '2':
                    await CheckOllamaStatusAsync();
                    break;
                case '3':
                    await TestVoiceAsync();
                    break;
                case '4':
                    ResetElevenLabsConfiguration();
                    break;
                case '5':
                    Console.WriteLine("To modify settings manually, edit the settings.json file in the application directory.");
                    Console.WriteLine($"Settings file location: {Path.GetFullPath("settings.json")}");
                    break;
                case '0':
                    return;
                default:
                    Console.WriteLine("Invalid option.");
                    break;
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to return to menu...");
            Console.ReadKey();
        }

        private static async Task ReconfigureElevenLabsAsync()
        {
            var elevenLabsHelper = new ElevenLabsSetupHelper(_settingsManager);

            // Reset to default state first
            var settings = _settingsManager.Settings;
            settings.ElevenLabs.ApiKey = "YOUR_ELEVENLABS_API_KEY_HERE";
            settings.ElevenLabs.Enabled = false;
            _settingsManager.UpdateSettings(settings);

            Console.WriteLine("Reconfiguring ElevenLabs...");
            await elevenLabsHelper.EnsureElevenLabsConfiguredAsync();
        }

        private static async Task CheckOllamaStatusAsync()
        {
            Console.WriteLine("Checking Ollama status...");

            var httpClient = new System.Net.Http.HttpClient();
            var ollamaInstaller = new OllamaInstaller(httpClient);

            if (await ollamaInstaller.IsOllamaAvailableAsync())
            {
                Console.WriteLine("✓ Ollama is running and available");

                // Check if model is available
                if (await _ollamaService.IsModelAvailableAsync())
                {
                    Console.WriteLine($"✓ Model '{_settingsManager.Settings.Ollama.ModelName}' is available");
                }
                else
                {
                    Console.WriteLine($"⚠ Model '{_settingsManager.Settings.Ollama.ModelName}' is not available");
                    Console.WriteLine("Would you like to download it now? (y/n)");
                    var response = Console.ReadKey();
                    Console.WriteLine();

                    if (response.KeyChar == 'y' || response.KeyChar == 'Y')
                    {
                        Console.WriteLine("Downloading model...");
                        await _ollamaService.PullModelAsync();
                    }
                }
            }
            else
            {
                Console.WriteLine("✗ Ollama is not available");

                if (ollamaInstaller.IsOllamaInstalled())
                {
                    Console.WriteLine("Ollama is installed but not running. Attempting to start...");
                    if (await ollamaInstaller.TryStartOllamaAsync())
                    {
                        Console.WriteLine("✓ Ollama started successfully");
                    }
                    else
                    {
                        Console.WriteLine("✗ Failed to start Ollama automatically");
                        Console.WriteLine("Try running 'ollama serve' in a command prompt/terminal");
                    }
                }
                else
                {
                    Console.WriteLine("Ollama is not installed.");
                    Console.WriteLine("Would you like to install it now? (y/n)");
                    var response = Console.ReadKey();
                    Console.WriteLine();

                    if (response.KeyChar == 'y' || response.KeyChar == 'Y')
                    {
                        await ollamaInstaller.GuideUserThroughInstallationAsync();
                    }
                }
            }
        }

        private static void ResetElevenLabsConfiguration()
        {
            var elevenLabsHelper = new ElevenLabsSetupHelper(_settingsManager);
            elevenLabsHelper.ResetElevenLabsConfiguration();
            Console.WriteLine("ElevenLabs configuration has been reset.");
        }
    }
}
