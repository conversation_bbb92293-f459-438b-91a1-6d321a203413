<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <LangVersion>8.0</LangVersion>
    <PlatformTarget>x64</PlatformTarget>
    <ImplicitUsings>disable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\FsConnect\src\CTrue.FsConnect\CTrue.FsConnect.csproj" />
    <ProjectReference Include="..\FsConnect\src\CTrue.FsConnect.Managers\CTrue.FsConnect.Managers.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="NAudio" Version="2.2.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
    <PackageReference Include="Whisper.net" Version="1.7.0" />
    <PackageReference Include="Whisper.net.Runtime" Version="1.7.0" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Microsoft.FlightSimulator.SimConnect">
      <HintPath>..\FsConnect\src\Dependencies\SimConnect\lib\net461\Microsoft.FlightSimulator.SimConnect.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <None Include="..\FsConnect\src\Dependencies\SimConnect\build\SimConnect.dll" Visible="false">
      <Link>%(FileName)%(Extension)</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="airports.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="settings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
